import {
  Advantages,
  DynamicDelayedSurveyMonkey,
  DynamicFAQ,
  HowItWorks,
  NewHero,
} from '@/src/app/_components';

export default async function HomePage() {
  return (
    <>
      <DynamicDelayedSurveyMonkey />

      {/* Hero Section with Background Image */}
      <div className="relative w-full">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
          style={{
            backgroundImage: 'url(/images/background-image.png)',
          }}
        />

        {/* Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-100" />

        {/* Content */}
        <section className="relative mx-auto max-w-7xl px-8 py-14 md:py-20 lg:px-12">
          <NewHero />
        </section>
      </div>

      {/* Como funciona Section */}
      <section className="mx-auto max-w-7xl px-8 py-16 lg:px-12">
        <HowItWorks />
      </section>

      {/* Vantagens Section */}
      <section className="mx-auto max-w-7xl px-8 py-16 lg:px-12">
        <Advantages />
      </section>

      {/* FAQ Section */}
      <section className="mx-auto max-w-7xl px-8 py-16 lg:px-12">
        <DynamicFAQ />
      </section>
    </>
  );
}
