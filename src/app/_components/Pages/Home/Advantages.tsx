'use client';

import { Icon } from '@/src/app/_components';

export const Advantages = () => {
  const advantages = [
    {
      title: 'Garantia de qualidade',
      description: 'Serviço com qualidade, sem frustrações',
    },
    {
      title: 'Agendamento imediato',
      description: 'Agende seu serviço para o mesmo dia ou dia seguinte',
    },
    {
      title: 'Preço fechado',
      description: 'Sem surpresas, você sabe exatamente quanto vai pagar',
    },
  ];

  return (
    <div className="mx-auto">
      <div className="flex flex-col gap-6 md:gap-8">
        {advantages.map((advantage, index) => (
          <div key={index} className="rounded-lg bg-white p-6 shadow-sm">
            <div className="flex items-start gap-4">
              {/* Icon */}
              <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-r from-amber-500 to-yellow-400">
                <Icon name="ChevronRight" className="h-4 w-4 text-white" strokeWidth={3} />
              </div>

              {/* Content */}
              <div className="flex-1">
                <h3 className="text-xl font-bold text-slate-900">{advantage.title}</h3>
                <p className="mt-2 text-base text-slate-600">{advantage.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
