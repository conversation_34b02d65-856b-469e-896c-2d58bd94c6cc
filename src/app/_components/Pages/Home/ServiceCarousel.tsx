'use client';

import { Card, CardContent } from '@/src/app/_components';
import { useAnalyticsEventGeneric } from '@/src/app/_hooks';
import { useCarouselNavigation } from '@/src/app/_hooks/useCarouselNavigation';
import { CarouselSubcategory, ServiceCarouselProps } from '@/src/app/_interfaces';
import { handleServiceRedirect } from '@/src/app/_utils/navigationUtils';
import { getLowestPrice } from '@/src/app/_utils/priceUtils';
import { ArrowLeft, ArrowRight, ChevronRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

export function ServiceCarousel({ services, showSubcategories = true }: ServiceCarouselProps) {
  const router = useRouter();
  const { sendEvent } = useAnalyticsEventGeneric();

  // Use custom hook for carousel navigation
  const {
    carouselRef,
    canScrollLeft,
    canScrollRight,
    showArrows,
    handleNext,
    handlePrev,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  } = useCarouselNavigation({ dependencies: [services] });

  return (
    <div className="bg-blue relative">
      <h2 className="mb-4 text-xl font-extrabold lg:mb-6 lg:text-3xl">
        Disponível para agendar agora
      </h2>

      {/* Navigation buttons - only shown when needed */}
      {showArrows && (
        <div className="absolute right-0 top-0 z-10 hidden gap-2 lg:flex">
          <button
            onClick={handlePrev}
            disabled={!canScrollLeft}
            className={`p-2 transition-opacity ${
              canScrollLeft
                ? 'text-gray-500 hover:text-gray-700'
                : 'cursor-not-allowed text-gray-300'
            }`}
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <button
            onClick={handleNext}
            disabled={!canScrollRight}
            className={`p-2 transition-opacity ${
              canScrollRight
                ? 'text-gray-500 hover:text-gray-700'
                : 'cursor-not-allowed text-gray-300'
            }`}
          >
            <ArrowRight className="h-6 w-6" />
          </button>
        </div>
      )}

      {/* Carousel container */}
      <div className="relative">
        <div
          ref={carouselRef}
          className="no-scrollbar -mx-8 flex gap-4 overflow-x-auto px-7 will-change-transform lg:mx-0 lg:px-0"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {services.map((category) => {
            const service = category.subcategories?.[0]?.services?.[0] || null;
            const lowestPrice = getLowestPrice(category);

            return (
              <Card
                key={category.id}
                className="w-[232px] flex-shrink-0 transform-gpu overflow-hidden border-none shadow-none"
              >
                {service?.imageUrl ? (
                  <img
                    src={service.imageUrl}
                    alt={category.name}
                    className="h-32 w-full rounded-xl object-cover"
                  />
                ) : (
                  <div className="flex h-40 w-full items-center justify-center rounded-xl bg-gray-200">
                    <span className="text-gray-500">Sem imagem</span>
                  </div>
                )}
                <CardContent className="flex flex-col p-4">
                  <h3 className="mt-4 text-base font-bold">{category.name}</h3>

                  {/* Menor preço */}
                  {lowestPrice !== null && (
                    <div>
                      <p className="mt-2 text-xs font-light text-gray-600">A partir de</p>
                      <p className="text-lg font-bold">
                        R$ {lowestPrice.toFixed(2).replace('.', ',')}
                      </p>
                    </div>
                  )}

                  {/* Lista de subcategorias com links */}
                  {showSubcategories && (
                    <ul className="mt-4 flex flex-col gap-1 overflow-hidden text-base">
                      {category.subcategories.map((subcategory: CarouselSubcategory) => (
                        <li
                          key={`${category.id}-${subcategory.id}`}
                          className="flex min-h-[40px] items-center p-2"
                        >
                          <ChevronRight className="mr-1 mt-0.5 h-7 w-7 flex-shrink-0 text-gray-500" />
                          <button
                            onClick={() => {
                              sendEvent(
                                `home_click_carousel_${subcategory.name.replace(/\s+/g, '_').toLowerCase()}`
                              );
                              handleServiceRedirect(
                                router,
                                subcategory.slug,
                                subcategory.services,
                                subcategory.name
                              );
                            }}
                            className="w-full break-words text-left text-base text-gray-500 hover:underline"
                          >
                            {subcategory.name}
                          </button>
                        </li>
                      ))}
                    </ul>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
}
