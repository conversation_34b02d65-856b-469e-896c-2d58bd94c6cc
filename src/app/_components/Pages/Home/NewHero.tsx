import Image from 'next/image';

export const NewHero = () => {
  // Service cards data for the deck visualization
  const serviceCards = [
    {
      id: 1,
      title: 'Eletricista',
      image: '/images/servicos/europ/eletricista/eletricista_instalacao_luminaria_plafon.webp',
      price: 'R$ 320,00',
      provider: 'EUROP ASSISTANCE',
    },
    {
      id: 2,
      title: 'Encanador',
      image: '/images/servicos/europ/encanador/encanador_torneira.webp',
      price: 'R$ 180,00',
      provider: 'EUROP ASSISTANCE',
    },
    {
      id: 3,
      title: 'Chaveiro',
      image: '/images/servicos/europ/chaveiro/chaveiro_troca_segredo_fechadura.webp',
      price: 'R$ 180,00',
      provider: 'EUROP ASSISTANCE',
    },
  ];

  return (
    <div className="flex flex-col items-stretch gap-10 lg:flex-row lg:gap-16">
      {/* Left Content */}
      <div className="flex flex-col justify-center gap-4 lg:flex-1">
        <h1 className="text-4xl font-extrabold leading-tight text-black md:text-5xl lg:text-6xl">
          <strong className="bg-gradient-to-r from-amber-500 via-yellow-400 to-yellow-300 bg-clip-text text-transparent">
            Serviço garantido,
          </strong>
          <br />
          seguro e sem complicação.
        </h1>
        <p className="text-lg font-medium text-black md:text-xl">
          O GetNinjas ajuda você a contratar o serviço que você precisa com segurança e agilidade,
          em parceria com a Europ Assistance.
        </p>
      </div>

      {/* Right Content - Service Cards Deck */}
      <div className="flex justify-center lg:flex-1">
        <div className="relative h-80 w-full max-w-md lg:h-96">
          {/* Card deck positioned according to Figma design */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative h-full w-full scale-75 md:scale-90 lg:scale-100">
              {/* Background Cards (Yellow/Black Theme) */}
              {/* Card 6 - Furthest back */}
              <div className="absolute right-8 top-20 z-10 h-48 w-36 rotate-12 transform-gpu rounded-2xl bg-gradient-to-br from-gray-800 to-gray-900 p-1 shadow-lg">
                <div className="h-full w-full rounded-xl bg-gray-700 opacity-60"></div>
              </div>

              {/* Card 5 - Back */}
              <div className="absolute right-4 top-16 z-20 h-52 w-40 rotate-6 transform-gpu rounded-2xl bg-gradient-to-br from-amber-500 to-yellow-400 p-1 shadow-xl">
                <div className="h-full w-full rounded-xl bg-gradient-to-br from-yellow-50 to-amber-50 opacity-80"></div>
              </div>

              {/* Card 4 - Middle back */}
              <div className="absolute right-0 top-12 z-30 h-56 w-44 rotate-3 transform-gpu rounded-2xl bg-gradient-to-br from-gray-700 to-gray-800 p-1 shadow-xl">
                <div className="h-full w-full rounded-xl bg-gray-600 opacity-70"></div>
              </div>

              {/* Service Cards (Front 3) */}
              {serviceCards.map((card, index) => (
                <div
                  key={card.id}
                  className={`absolute transform-gpu transition-all duration-300 hover:scale-105 ${
                    index === 0
                      ? 'left-0 top-0 z-50 h-64 w-48 rotate-[-8deg] shadow-2xl' // Front card
                      : index === 1
                        ? 'left-8 top-4 z-40 h-60 w-44 rotate-[-4deg] shadow-xl' // Second card
                        : 'left-16 top-8 z-30 h-56 w-44 rotate-0 shadow-lg' // Third card
                  } rounded-2xl bg-white p-3`}
                >
                  {/* Card Content */}
                  <div className="h-full w-full overflow-hidden rounded-xl">
                    {/* Service Image */}
                    <div className="relative h-32 w-full overflow-hidden rounded-lg">
                      <Image
                        src={card.image}
                        alt={card.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 200px, 250px"
                      />
                    </div>

                    {/* Card Info */}
                    <div className="p-2">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-bold text-gray-900">{card.title}</h3>
                        {/* Provider Logo */}
                        <div className="h-6 w-6 overflow-hidden rounded-full bg-gray-100">
                          <Image
                            src="/images/logo_europ.png"
                            alt={card.provider}
                            width={24}
                            height={24}
                            className="object-contain"
                          />
                        </div>
                      </div>
                      <p className="mt-1 text-xs text-gray-600">{card.provider}</p>
                      <div className="mt-2">
                        <span className="text-sm font-bold text-green-600">{card.price}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
