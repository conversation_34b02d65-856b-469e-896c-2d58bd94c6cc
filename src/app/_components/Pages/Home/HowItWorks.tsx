'use client';

import { Icon, IconName } from '@/src/app/_components';

export const HowItWorks = () => {
  const steps: Array<{
    number: number;
    icon: IconName;
    title: string;
  }> = [
    {
      number: 1,
      icon: 'MousePointer',
      title: '<PERSON><PERSON><PERSON><PERSON> o serviço',
    },
    {
      number: 2,
      icon: 'Calendar',
      title: 'Escolha a melhor data',
    },
    {
      number: 3,
      icon: 'HardHat',
      title: '<PERSON><PERSON><PERSON> o profissional',
    },
    {
      number: 4,
      icon: 'Receipt',
      title: 'Aprove o pagamento',
    },
  ];

  return (
    <div className="mx-auto">
      {/* Section Title */}
      <div className="mb-8 rounded-xl bg-white p-6">
        <h2 className="text-xl font-bold text-slate-900">Como funciona?</h2>
      </div>

      {/* Steps Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {steps.map((step) => (
          <div key={step.number} className="rounded-2xl bg-white p-6 shadow-sm">
            {/* Step Number */}
            <div className="mb-4 flex items-center gap-4">
              <div className="flex h-8 w-8 items-center justify-center rounded-full border border-slate-200 bg-white text-slate-600">
                <span className="text-sm font-bold">{step.number}</span>
              </div>
            </div>

            {/* Icon and Title */}
            <div className="flex items-center gap-4">
              <Icon name={step.icon} className="h-6 w-6 text-slate-600" strokeWidth={2} />
              <h3 className="text-base font-semibold text-slate-900">{step.title}</h3>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
